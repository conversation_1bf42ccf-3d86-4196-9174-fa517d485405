version: 1
update_configs:
  # Keep package.json (& lockfiles) up to date as soon as
  # new versions are published to the npm registry
  - package_manager: 'javascript'
    directory: '{{cookiecutter.project_slug}}/frontend'
    update_schedule: 'monthly'
  # Keep Dockerfile up to date, batching pull requests weekly
  - package_manager: 'python'
    directory: '{{cookiecutter.project_slug}}/backend'
    update_schedule: 'monthly'
