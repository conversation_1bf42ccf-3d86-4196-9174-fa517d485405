server {
    listen 80;
    server_name {{cookiecutter.project_slug}};

    location / {
        proxy_set_header X-Forwarded-Host $host;
	    proxy_set_header X-Forwarded-Server $host;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

	    proxy_pass http://frontend:3000;

        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /api {
	    proxy_pass http://backend:8888/api;
	}
}
