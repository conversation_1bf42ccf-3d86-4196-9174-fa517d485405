name: build

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: |
          sudo python -m pip install --upgrade pip
          sudo pip install cookiecutter
      - name: Prettify code
        uses: creyD/prettier_action@v2.2
        with:
          prettier_options: --write **/*.{ts,tsx,md}
      - name: Black Code Formatter
        uses: lgeiger/black-action@master
        with:
          args: '{{cookiecutter.project_slug}}/backend --check --line-length 79'
      - name: Run tests
        run: |
          sudo ./scripts/test.sh
